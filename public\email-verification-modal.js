/**
 * Email Verification Modal Component
 * A modern modal for handling email verification during invitation flow
 */

(function(global) {
  'use strict';

  let isModalInitialized = false;
  let isClosing = false;
  let modalOverlay = null;
  let modalContent = null;
  let currentPromiseResolve = null;

  /**
   * Creates the modal HTML structure
   * @param {Object} options - Configuration options
   * @returns {string} HTML string for the modal
   */
  function createModalHTML(options) {
    const {
      title = 'Email Verification Required',
      message = 'We\'ve noticed you\'re trying to send an invitation. Please verify your email address first to continue.',
      confirmText = 'Send Verification Email',
      cancelText = 'Cancel',
    } = options;

    // Email verification icon
    const iconSvg = `
      <svg class="email-verification-modal-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
      </svg>
    `;

    return `
      <div class="email-verification-modal-overlay">
        <div class="email-verification-modal-content">
          <div class="email-verification-modal-header">
            ${iconSvg}
            <h2 class="email-verification-modal-title">${title}</h2>
            <button class="email-verification-modal-close">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
          <div class="email-verification-modal-body">
            <p class="email-verification-modal-message">${message}</p>
          </div>
          <div class="email-verification-modal-footer">
            <button class="email-verification-modal-cancel-button">${cancelText}</button>
            <button class="email-verification-modal-confirm-button">${confirmText}</button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Injects the required CSS styles
   */
  function injectCSS() {
    if (document.getElementById('email-verification-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'email-verification-modal-styles';
    style.textContent = `
      .email-verification-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(18, 28, 65, 0.3);
        backdrop-filter: blur(3px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3000;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .email-verification-modal-content {
        background: #fff;
        border-radius: 0.75rem;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        opacity: 0;
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .email-verification-modal-header {
        display: flex;
        align-items: center;
        padding: 1.25rem 1.5rem 0.75rem;
        position: relative;
      }

      .email-verification-modal-icon {
        width: 2rem;
        height: 2rem;
        margin-right: 0.75rem;
        color: #3B82F6;
      }

      .email-verification-modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin: 0;
        flex-grow: 1;
      }

      .email-verification-modal-close {
        background: none;
        border: none;
        color: #6B7280;
        cursor: pointer;
        padding: 0.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem;
        transition: background-color 0.2s, color 0.2s;
        position: absolute;
        top: 1rem;
        right: 1rem;
      }

      .email-verification-modal-close:hover {
        background-color: #F3F4F6;
        color: #374151;
      }

      .email-verification-modal-body {
        padding: 0.75rem 1.5rem 1.25rem;
      }

      .email-verification-modal-message {
        margin: 0;
        color: #4B5563;
        font-size: 1rem;
        line-height: 1.5;
      }

      .email-verification-modal-footer {
        display: flex;
        justify-content: flex-end;
        padding: 1rem 1.5rem 1.5rem;
        gap: 0.75rem;
      }

      .email-verification-modal-cancel-button {
        padding: 0.5rem 1rem;
        background-color: #F3F4F6;
        color: #374151;
        border: none;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .email-verification-modal-cancel-button:hover {
        background-color: #E5E7EB;
      }

      .email-verification-modal-confirm-button {
        padding: 0.5rem 1rem;
        background-color: #3B82F6;
        color: white;
        border: none;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .email-verification-modal-confirm-button:hover {
        background-color: #2563EB;
      }

      .email-verification-modal-confirm-button:disabled {
        background-color: #9CA3AF;
        cursor: not-allowed;
      }

      @media (max-width: 640px) {
        .email-verification-modal-content {
          width: 95%;
          max-width: none;
        }
        
        .email-verification-modal-footer {
          flex-direction: column-reverse;
        }
        
        .email-verification-modal-cancel-button,
        .email-verification-modal-confirm-button {
          width: 100%;
          padding: 0.75rem 1rem;
        }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Initialize event listeners for the modal
   */
  function initializeEventListeners() {
    // Close button
    const closeButton = document.querySelector('.email-verification-modal-close');
    closeButton.addEventListener('click', () => hideEmailVerificationModal(false));

    // Cancel button
    const cancelButton = document.querySelector('.email-verification-modal-cancel-button');
    cancelButton.addEventListener('click', () => hideEmailVerificationModal(false));

    // Confirm button
    const confirmButton = document.querySelector('.email-verification-modal-confirm-button');
    confirmButton.addEventListener('click', () => hideEmailVerificationModal(true));

    // Click outside to close
    modalOverlay.addEventListener('click', (event) => {
      if (event.target === modalOverlay) {
        hideEmailVerificationModal(false);
      }
    });

    // Escape key to close
    document.addEventListener('keydown', handleKeyDown);
  }

  /**
   * Handle keydown events for the modal
   * @param {KeyboardEvent} event - The keyboard event
   */
  function handleKeyDown(event) {
    if (event.key === 'Escape' && isModalInitialized && !isClosing) {
      hideEmailVerificationModal(false);
    }
  }

  /**
   * Shows the email verification modal
   * @param {Object} options - Configuration options
   * @returns {Promise<boolean>} - Resolves to true if confirmed, false if cancelled
   */
  function showEmailVerificationModal(options = {}) {
    isClosing = false;

    // Remove existing modal if it exists
    const existingModal = document.querySelector('.email-verification-modal-overlay');
    if (existingModal) {
      existingModal.parentNode.removeChild(existingModal);
      isModalInitialized = false;
    }

    // Remove existing keydown listener
    document.removeEventListener('keydown', handleKeyDown);

    // Create modal
    injectCSS();
    const modalHTML = createModalHTML(options);
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;

    // Add to body
    document.body.appendChild(modalContainer.firstElementChild);

    modalOverlay = document.querySelector('.email-verification-modal-overlay');
    modalContent = document.querySelector('.email-verification-modal-content');

    // Initialize event listeners
    initializeEventListeners();

    isModalInitialized = true;

    // Show modal with animation
    setTimeout(() => {
      if (isClosing) return;

      modalOverlay.style.opacity = '1';
      modalContent.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }, 10);

    return new Promise(resolve => {
      currentPromiseResolve = resolve;
    });
  }

  /**
   * Hides the email verification modal
   * @param {boolean} result - The result of the modal (true for confirm, false for cancel)
   */
  function hideEmailVerificationModal(result) {
    isClosing = true;

    // Remove keydown listener
    document.removeEventListener('keydown', handleKeyDown);

    // Animate closing
    if (modalOverlay) {
      modalOverlay.style.opacity = '0';
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'scale(0.95)';

      // Remove after animation completes
      setTimeout(() => {
        if (modalOverlay && modalOverlay.parentNode) {
          modalOverlay.parentNode.removeChild(modalOverlay);
        }
        isModalInitialized = false;

        // Resolve promise if it exists
        if (currentPromiseResolve) {
          currentPromiseResolve(result);
          currentPromiseResolve = null;
        }
      }, 300);
    }
  }

  // Public API
  global.EmailVerificationModal = {
    show: showEmailVerificationModal
  };
})(typeof window !== 'undefined' ? window : global);
