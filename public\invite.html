<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Include Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2c4da5f3',
                        secondary: '#1e3a8a',
                    }
                }
            }
        }
    </script>
    <!-- Include PapaParse Library -->
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.3.0/papaparse.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <script src="https://www.paypal.com/sdk/js?client-id=test&currency=GBP"></script>
    <style>
        /* Import Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        /* Apply font to the body */
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
        }


        /* Glass container styling */
        .glass-container {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
        }

        /* Additional styling adjustments */
        .tab-button.active-tab {
            background-color: #2c4da5f3;
            color: #ffffff;
        }

        .tab-button.inactive-tab {
            background-color: #f3f4f6;
            color: #4b5563;
        }

        .send-button {
            background-color: #2c4da5f3;
        }

        .send-button:hover {
            background-color: #1e3a8a;
        }

        /* Text styling */
        .text-secondary {
            color: #2c4da5f3 !important;
        }

        .text-lg.text-gray-700 {
            font-size: 0.9rem;
            color: #4a5568 !important;
        }

        .blue-text {
            color: #2c4da5f3;
        }

        /* Smooth transition for form sections */
        .form-container {
            /* Allows dynamic height */
        }

        .form-section {
            transition: opacity 0.5s ease-in-out, max-height 0.5s ease-in-out;
            overflow: hidden;
            opacity: 0;
            max-height: 0;
        }

        .form-section.active {
            opacity: 1;
            max-height: 1000px; /* Adjust as needed */
        }

        .tab {
            cursor: pointer;
            padding: 10px 20px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .active-tab {
            border-bottom-color: #4A90E2;
            font-weight: bold;
        }

        /* Invitee List Styles */
        .invitees-list {
            overflow-x: auto;
            display: flex;
            flex-wrap: nowrap;
            gap: 1rem;
            padding-bottom: 0.5rem;
        }

        .invitee-item {
            flex: 0 0 auto;
            background-color: #f9fafb;
            padding: 0.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            font-size: 0.85rem;
            min-width: 180px; /* Adjust as needed */
        }

        .invitee-item:hover {
            background-color: #f3f4f6;
        }

        /* Improve aesthetics */
        .internal-button {
            transition: background-color 0.3s ease;
        }

        .internal-button.add-button {
            background-color: #4CAF50; /* Green */
        }

        .internal-button.add-button:hover {
            background-color: #45A049;
        }

        .internal-button.upload-button {
            background-color: #2196F3; /* Blue */
        }

        .internal-button.upload-button:hover {
            background-color: #0b7dda;
        }

        /* Hover effects for summary cards */
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* Adjust form inputs */
        .form-group input {
            background-color: #fff;
        }

        .form-group input:focus {
            border-color: #2c4da5f3;
            box-shadow: 0 0 0 1px #2c4da5f3;
        }

        /* Reduce spacing in form sections */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        /* Adjust spacing for buttons */
        .form-group.mt-6 {
            margin-top: 1.5rem;
        }

        /* Shake animation */
        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }

        .shake {
            animation: shake 0.5s;
        }
        #detailsContainer {
        transition: max-height 0.3s ease-out;
        max-height: 0;
        overflow: hidden;
        }

    #detailsContainer.hidden {
    display: none;
        }
        #detailsContainer ul {
     max-height: 200px;
    overflow-y: auto;
    }

    #detailsContainer li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
    }

    #detailsContainer li:last-child {
    border-bottom: none;
    }

    .summary {
  color: #333;
  max-width: 800px;
  margin: 0 auto;
  padding: 30px;
  background-color: #f0f4f8;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.summary-header {
  border-bottom: 2px solid #d1e1f1;
  padding-bottom: 15px;
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-header h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1a5f7a;
  margin: 0;
}

.summary-header button {
  background: none;
  border: none;
  color: #3498db;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.summary-header button:hover {
  color: #2980b9;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 25px;
}

.summary-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.summary-card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.summary-card h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1a5f7a;
  margin: 0 0 10px 0;
}

.summary-card p {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
}

#detailsContainer {
  margin-top: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

#detailsContainer h5 {
  font-size: 1.1rem;
  color: #1a5f7a;
  margin-bottom: 15px;
  border-bottom: 1px solid #d1e1f1;
  padding-bottom: 10px;
}

#detailsContainer ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.8;
  color: #34495e;
}

#detailsContainer ul li {
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

#detailsContainer ul li::before {
  content: '•';
  color: #3498db;
  position: absolute;
  left: 0;
  top: 0;
}
.summary-card.emails-sent h4,
.summary-card.emails-failed h4 {
  color: #1a5f7a;
}
/* Fix for truncated email domains in Sent Emails section */
#sentEmailsList li,
#failedEmailsList li,
.summary-grid .summary-card span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: inline-block;
}

/* Fix for the email domain display in the invitation summary */
#inviteContainer {
  position: relative;
  overflow: hidden;
}

#sentEmailsList {
  width: 100%;
  word-break: break-all;
}

/* Email list styling */
.sent-emails {
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

/* Fix summary cards on smaller screens */
@media (max-width: 768px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .summary-card {
    padding: 0.75rem;
  }

  /* Better handling of email text in the summary cards */
  .summary-card .text-sm {
    font-size: 0.75rem;
  }
}

/* Improved responsive styling for the entire invitation view */
@media (max-width: 640px) {
  .glass-container {
    padding: 1.25rem;
    max-width: 100%;
  }

  /* Ensure email text doesn't overflow container */
  #detailsContainer ul li {
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
    padding-right: 0.5rem;
  }
}

  .notification {
            position: fixed;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 6px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 9999;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            font-weight: 500;
        }

        .notification.success {
            background-color: #48bb78;
            color: white;
        }

        .notification.error {
            background-color: #FEE2E2;
            color: #991B1B;
            border: 1px solid #FCA5A5;
        }

        .notification.warning {
            background-color: #FEF3C7;
            color: #92400E;
            border: 1px solid #FCD34D;
        }
        .credit-package {
    transition: all 0.3s ease;
}

.credit-package:hover {
    transform: translateY(-2px);
}

.select-package-btn {
    transition: all 0.2s ease;
}

.credit-prompt {
    transition: all 0.3s ease;
}

/* Assessment Type Selector Styles */
.assessment-selector-container {
    display: flex;
    justify-content: center;
    margin: 2rem 0 1rem;
}

.assessment-selector {
    display: flex;
    background-color: #f3f4f6;
    border-radius: 12px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assessment-type {
    padding: 0.7rem 1.2rem;  /* Slightly reduced padding */
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;  /* Reduced from 0.9rem */
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6B7280;  /* Default to grey for inactive state */
}

.assessment-type:hover {
    background-color: rgba(44, 77, 165, 0.1);
}

.assessment-type.active-assessment {
    background-color: #2c4da5f3;
    color: white;  /* Only active state is white */
}

.assessment-type svg {
    width: 1.2rem;  /* Slightly reduced from 1.25rem */
    height: 1.2rem;  /* Slightly reduced from 1.25rem */
}
    </style>
</head>
<body class="bg-gray-100">
    <div class="glass-container">
        <!-- Header -->
        <div class="flex flex-col items-center mb-8">
            <div class="flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-secondary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76" />
              </svg>
              <h1 class="text-xl font-semibold text-secondary">Send Invitations</h1>
            </div>
            <p class="text-base text-gray-700">Invite your friends and colleagues effortlessly</p>
          </div>

        <!-- Tabs for Manual Entry and CSV Upload -->
        <div class="flex justify-center mb-4">
            <button id="manualTab" class="tab-button active-tab px-6 py-2 rounded-full mr-4" style="font-size: 12px;">Manual Entry</button>
            <button id="csvTab" class="tab-button inactive-tab px-6 py-2 rounded-full" style="font-size: 12px;">Upload CSV</button>
        </div>

        <!-- Form Container -->
        <div class="form-container">
            <!-- Manual Entry Section -->
            <div id="manualSection" class="form-section active">
                <div>
                    <div class="flex space-x-4">
                        <div class="form-group w-1/2">
                            <label for="firstNameInput" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                            <input type="text" id="firstNameInput" placeholder="Enter first name" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div class="form-group w-1/2">
                            <label for="lastNameInput" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                            <input type="text" id="lastNameInput" placeholder="Enter last name" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                    </div>
                    <div class="form-group mt-4">
                        <label for="emailInput" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="emailInput" placeholder="Enter email address" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <div class="form-group text-center mt-4">
                        <button type="button" id="addButton" class="internal-button add-button px-6 py-2 rounded-full text-white">Add</button>
                    </div>
                </div>
            </div>

            <!-- CSV Upload Section -->
            <div id="csvSection" class="form-section">
                <div>
                    <div class="form-group">
                        <label for="csvFileInput" class="block text-sm font-medium text-gray-700 mb-1">Upload CSV File</label>
                        <input type="file" id="csvFileInput" accept=".csv" class="w-full">
                    </div>
                    <div class="form-group">
                        <p id="csvInstruction" class="text-sm text-gray-500">CSV should contain 'First Name', 'Last Name', and 'Email' columns.</p>
                    </div>
                    <div class="form-group text-center">
                        <button type="button" id="uploadButton" class="internal-button upload-button px-6 py-2 rounded-full text-white">Upload</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invitees List -->
        <div class="invitees-list mt-6" id="inviteesContainer">
            <!-- Invitees will be dynamically added here by JavaScript -->
        </div>

        <!-- Add Assessment Type Selector before Send Invitations Button -->
        <div class="assessment-selector-container">
            <div class="assessment-selector">
                <div id="digitalSkillsSelector" class="assessment-type active-assessment">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25" />
                    </svg>
                    Digital Skills
                </div>
                <div id="softSkillsSelector" class="assessment-type">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                    </svg>
                    Soft Skills
                </div>
            </div>
        </div>

        <!-- Send Invitations Button -->
        <div class="form-group mt-4">
            <button id="sendInvitationsButton" class="send-button w-full py-3 text-white font-semibold rounded-lg hover:bg-primary-dark">Send Invitations</button>
        </div>

      <!-- Invitation Summary Section -->
<div class="summary mt-6">
  <!-- Header -->
  <div class="summary-header flex justify-between items-center mb-4">
    <h3 id="invitation-summary" class="text-base font-semibold text-gray-800">
      Invitation Summary
    </h3>
    <button
      id="viewDetailsBtn"
      class="text-blue-500 text-xs hover:text-blue-600 transition-colors"
    >
      View Details
    </button>
  </div>

  <!-- Summary Cards -->
  <div class="summary-grid grid grid-cols-1 md:grid-cols-3 gap-3">
    <!-- Emails Sent Card -->
    <div
      class="summary-card flex items-center justify-between bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300"
    >
      <div class="flex items-center space-x-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 text-blue-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
        <p class="text-gray-600 text-sm font-medium">Emails Sent</p>
      </div>
      <span id="emailsSent" class="text-base font-semibold text-blue-600">0</span>
    </div>

    <!-- Failed Emails Card -->
    <div
      class="summary-card flex items-center justify-between bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300"
    >
      <div class="flex items-center space-x-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 text-red-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <p class="text-gray-600 text-sm font-medium">Failed Emails</p>
      </div>
      <span id="emailsFailed" class="text-base font-semibold text-red-600">0</span>
    </div>

    <!-- Credits Left Card -->
    <div
      class="summary-card flex items-center justify-between bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300"
    >
      <div class="flex items-center space-x-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 text-green-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <p class="text-gray-600 text-sm font-medium">Credits Left</p>
      </div>
      <span id="creditsLeft" class="text-base font-semibold text-green-600">0</span>
    </div>
  </div>

  <!-- Details Section -->
  <div id="detailsContainer" class="mt-4 hidden">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Sent Emails List -->
      <div>
        <h5 class="text-sm font-semibold mb-2">Sent Emails</h5>
        <ul
          id="sentEmailsList"
          class="list-disc list-inside bg-white p-3 rounded-md shadow-sm text-sm text-gray-600"
        ></ul>
      </div>
      <!-- Failed Emails List -->
      <div>
        <h5 class="text-sm font-semibold mb-2">Failed Emails</h5>
        <ul
          id="failedEmailsList"
          class="list-disc list-inside bg-white p-3 rounded-md shadow-sm text-sm text-gray-600"
        ></ul>
      </div>
    </div>
  </div>
</div>


    <!-- Include Stripe config and scripts -->
    <script src="stripe-config.js"></script>
    <script src="warning-modal.js"></script>
    <script src="email-verification-modal.js"></script>
    <script src="subscription-modal.js"></script>
    <script src="topup-modal.js"></script>

    <!-- Ensure modal is loaded before invite.js -->
    <script>
        // Wait for EmailVerificationModal to be available before loading invite.js
        function loadInviteScript() {
            if (window.EmailVerificationModal && typeof window.EmailVerificationModal.show === 'function') {
                console.log('EmailVerificationModal confirmed available, loading invite.js');
                const script = document.createElement('script');
                script.src = 'invite.js';
                document.body.appendChild(script);
            } else {
                console.log('EmailVerificationModal not yet available, retrying...');
                setTimeout(loadInviteScript, 100);
            }
        }

        // Start loading invite.js after a short delay
        setTimeout(loadInviteScript, 100);
    </script>
</body>
</html>
