let employeeCount = null;
let currentUser = null;
let userCompany = null;
let pageHistory = [];
let navigationStack = [];
let previousContentType = null;
let tourInitialized = false;
let navigationState = {
    stack: [],
    currentIndex: -1
  };
  function shouldEnableDashboard() {
    return employeeCount >= 1;
}

function loadIntroJS() {
    return new Promise((resolve, reject) => {
        if (window.introJs) {
            resolve();
            return;
        }

        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/intro.js/7.0.1/introjs.min.css';
        document.head.appendChild(cssLink);

        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/intro.js/7.0.1/intro.min.js';
        script.async = true;

        script.onload = () => {
            if (window.introJs) {
                resolve();
            } else {
                reject(new Error('introJs not found after script load'));
            }
        };

        script.onerror = () => {
            reject(new Error('Failed to load intro.js'));
        };

        document.head.appendChild(script);
    });
}
function verifyTourState(user) {
    console.log('Verifying tour state for user:', user.email);

    return db.collection('Admins').doc(user.email).get()
        .then(doc => {
            const adminData = doc.data();
            console.log('Admin data:', {
                hasCompletedTour: adminData.hasCompletedTour,
                employeeCount: employeeCount,
                tourCompletedAt: adminData.tourCompletedAt
            });

            // Ensure hasCompletedTour is explicitly boolean
            if (typeof adminData.hasCompletedTour === 'undefined') {
                console.log('hasCompletedTour not set, initializing to false');
                return db.collection('Admins').doc(user.email).update({
                    hasCompletedTour: false
                }).then(() => false);
            }

            return adminData.hasCompletedTour;
        });
}


const API_URL = window.location.hostname === 'localhost'
  ? 'http://localhost:3000'
  : 'https://assessment-dashboard-oowu.onrender.com';

window.addEventListener('popstate', handlePopState);

pushToHistory('dashboard');

// Update the loading animation initialization
const loadingAnimation = lottie.loadAnimation({
    container: document.getElementById('loading-animation'),
    renderer: 'svg',
    loop: true,
    autoplay: true,
    path: 'assess_loading.json',
    initialSegment: [0, 60], // Play first 60 frames if needed
});

// Set animation speed to 0.5x
loadingAnimation.setSpeed(0.5);

let loadingTexts = ["Gathering insights..", "Just a moment.....", "Loading....."];
let loadingTextElement = document.querySelector('.loading-text');
let index = 0;

setInterval(() => {
    loadingTextElement.textContent = loadingTexts[index];
    index = (index + 1) % loadingTexts.length;
}, 4000);

const loadingOverlay = document.getElementById('loading-overlay');

function showLoadingOverlay() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
}

function hideLoadingOverlay() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

function getMainContent() {
    console.log('Getting main content element...');
    let mainContent = document.querySelector('#main-content');
    if (!mainContent) {
      console.warn('Main content element not found. Creating a new one.');
      mainContent = document.createElement('div');
      mainContent.id = 'main-content';
      document.body.appendChild(mainContent);
    }
    return mainContent;
  }



// Function to show the login success overlay
function showLoginSuccessOverlay() {
    return new Promise((resolve) => {
      const overlay = document.getElementById('login-success-overlay');
      overlay.style.display = 'flex';  // Use flex to center the content
      overlay.style.alignItems = 'center';
      overlay.style.justifyContent = 'center';
      overlay.classList.remove('hidden');
      overlay.classList.add('visible');

      const animation = lottie.loadAnimation({
        container: document.getElementById('lottie-container'),
        renderer: 'svg',
        loop: false,
        autoplay: true,
        path: 'success.json'
      });

      animation.addEventListener('complete', () => {
        setTimeout(() => {
          hideLoginSuccessOverlay();
          resolve();
        }, 1000);
      });
    });
  }

  // Modify the hideLoginSuccessOverlay function
  function hideLoginSuccessOverlay() {
    const overlay = document.getElementById('login-success-overlay');
    overlay.classList.remove('visible');
    overlay.classList.add('hidden');
    overlay.style.display = 'none';  // Ensure it's completely hidden
  }


const firebaseConfig = {
    apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
    authDomain: "barefoot-elearning-app.firebaseapp.com",
    projectId: "barefoot-elearning-app",
    databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
    storageBucket: "barefoot-elearning-app.appspot.com",
    messagingSenderId: "170819735788",
    appId: "1:170819735788:web:223af318437eb5d947d5c9"
};


firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();
listenForDataChanges();

function listenForDataChanges() {
    if (!userCompany) {
        console.error('User company not set');
        return;
    }
    const companyRef = db.collection('companies').doc(userCompany);
    companyRef.collection('users').onSnapshot((snapshot) => {
        if (snapshot.docChanges().length > 0) {
            console.log('Data changes detected');
            sessionStorage.removeItem('dashboardData');
            sessionStorage.removeItem('dashboardLastFetchTime');
            sessionStorage.removeItem('tableData');
            sessionStorage.removeItem('tableLastFetchTime');

            if (currentContent === 'dashboard') {
                console.log('Updating dashboard content');
                updateDashboardContent();
            } else if (currentContent === 'assessments') {
                console.log('Updating assessments content');
                updateAssessmentsContent();
            }
        }
    }, (error) => {
        console.error('Error in onSnapshot:', error);
    });
}

function updateDashboardContent() {
    showLoadingOverlay();
    Promise.all([
        updateDashboardData(userCompany),
        initializeTable(userCompany)
    ])
    .then(([dashboardData, tableData]) => {
        console.log('Dashboard data updated:', dashboardData);
        console.log('Table data updated:', tableData);
        updateDashboardUI(dashboardData);
        updateTableUI(tableData);
        setupDashboardEventListeners(); // Re-attach dashboard event listeners
        hideLoadingOverlay();
    })
    .catch(error => {
        console.error('Error updating dashboard:', error);
        hideLoadingOverlay();
    });
}

function updateAssessmentsContent() {
    showLoadingOverlay();
    initializeAssessments(userCompany)
    .then(() => {
        updateAssessmentData(userCompany);
        addEventListenersToSendReminderIcons();
        addEmailEventListeners();
        hideLoadingOverlay();
    })
    .catch(error => {
        console.error('Error updating assessments:', error);
        hideLoadingOverlay();
    });
}

document.addEventListener('DOMContentLoaded', function () {
    console.log("DOM fully loaded and parsed")

    // Check for URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const fromParam = urlParams.get('from');
    const showSubscription = urlParams.get('showSubscription') === 'true';

    // If coming from success or cancel page, or showSubscription is true, show subscription modal
    if (fromParam === 'success' || fromParam === 'cancel' || showSubscription) {
        // Wait for subscription modal script to load
        const checkSubscriptionModal = setInterval(() => {
            if (window.SubscriptionModal) {
                clearInterval(checkSubscriptionModal);
                window.SubscriptionModal.show(true);
            }
        }, 500);

        // Clear interval after 10 seconds to prevent infinite checking
        setTimeout(() => clearInterval(checkSubscriptionModal), 10000);
    }

    // Initialize Navigaton Drawer and User Menu
    initializeNavigationDrawer();
    initializeUserMenu();
    listenForDataChanges();
    initializeNotificationButton();

    // Check if user is on a mobile device and show warning if needed
    // This handles cases where the user might already be logged in
    setTimeout(() => {
        if (window.MobileDetection && window.MobileWarning && currentUser) {
            if (window.MobileDetection.isMobileDevice() &&
                !window.MobileDetection.hasUserDismissedWarning() &&
                !window.MobileDetection.isWarningDismissedForSession()) {
                window.MobileWarning.show();
            }
        }
    }, 1000); // Small delay to ensure everything is loaded
});

firebase.auth().onAuthStateChanged((user) => {
    if (user) {
        currentUser = user;
        console.log('User is signed in:', currentUser);

        // Load subscription modal script first
        const subscriptionScript = document.createElement('script');
        subscriptionScript.src = 'subscription-modal.js';
        document.head.appendChild(subscriptionScript);

        showLoginSuccessOverlay().then(() => {
            const adminRef = db.collection('Admins').doc(user.email);
            adminRef.get()
                .then((doc) => {
                    if (doc.exists) {
                        const adminData = doc.data();
                        userCompany = adminData.company;
                        const hasCompletedTour = adminData.hasCompletedTour || false;

                        // Check if subscription has expired
                        let hasExpiredSubscription = false;
                        if (adminData.subscriptionEndDate) {
                            const endDate = adminData.subscriptionEndDate.toDate ?
                                adminData.subscriptionEndDate.toDate() :
                                new Date(adminData.subscriptionEndDate);
                            const now = new Date();
                            hasExpiredSubscription = endDate < now && adminData.subscriptionActive;

                            // If subscription has expired, update the database
                            if (hasExpiredSubscription) {
                                console.log('Subscription has expired, updating database');
                                db.collection('Admins').doc(user.email).update({
                                    subscriptionActive: false,
                                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                                });
                            }
                        }

                        // Check if user has an active subscription
                        const hasSubscription = adminData.paid ||
                            (adminData.subscriptionType &&
                             adminData.subscriptionType !== 'cancelled' &&
                             adminData.subscriptionActive &&
                             !hasExpiredSubscription);

                        // Check if user needs to select a subscription (subscriptionType is null)
                        const needsSubscription = adminData.subscriptionType === null;

                        return Promise.all([
                            verifyEmployeeCount(),
                            hasCompletedTour,
                            hasSubscription,
                            needsSubscription
                        ]);
                    } else {
                        throw new Error('Admin document not found');
                    }
                })
                .then(([verifiedCount, hasCompletedTour, hasSubscription, needsSubscription]) => {
                    employeeCount = verifiedCount;
                    console.log('Verified employee count:', employeeCount);
                    console.log('Subscription status:', { hasSubscription, needsSubscription });

                    setTimeout(() => {
                        updateNavigationState();
                    }, 0);

                    setupEmployeeCountListener();

                    // If user needs to select a subscription (subscriptionType is null), show required subscription modal
                    if (needsSubscription) {
                        console.log('User needs to select a subscription');
                        return window.SubscriptionModal.show(false, true, true).then(result => {
                            console.log('Subscription modal result:', result);
                            // If user selected a subscription, load dashboard, otherwise load invite page
                            if (result && result.success) {
                                return loadMainContent().then(() => {
                                    if (!hasCompletedTour) {
                                        return initializeDashboardTour(false);
                                    }
                                });
                            } else {
                                // Load invite page as default for users without subscription
                                const mainContent = document.querySelector('#main-content');
                                if (mainContent) {
                                    return loadInvitePage(mainContent);
                                }
                            }
                        });
                    }
                    // If user doesn't have an active subscription, show subscription modal and then load invite page
                    else if (!hasSubscription) {
                        console.log('User has no active subscription');
                        return window.SubscriptionModal.show(false, false, true).then(result => {
                            console.log('Subscription modal result:', result);
                            // If user selected a subscription, load dashboard, otherwise load invite page
                            if (result && result.success) {
                                return loadMainContent().then(() => {
                                    if (!hasCompletedTour) {
                                        return initializeDashboardTour(false);
                                    }
                                });
                            } else {
                                // Load invite page as default for users without subscription
                                const mainContent = document.querySelector('#main-content');
                                if (mainContent) {
                                    return loadInvitePage(mainContent);
                                }
                            }
                        });
                    } else {
                        console.log('User has an active subscription');
                        return loadMainContent().then(() => {
                            if (!hasCompletedTour) {
                                return initializeDashboardTour(false);
                            }
                        });
                    }
                })
                .then(() => {
                    startPendingAssessmentCheck();
                    listenForDataChanges();
                    initializeUserMenu();

                    // Check if user is on a mobile device and show warning if needed
                    if (window.MobileDetection && window.MobileWarning) {
                        if (window.MobileDetection.isMobileDevice() &&
                            !window.MobileDetection.hasUserDismissedWarning() &&
                            !window.MobileDetection.isWarningDismissedForSession()) {
                            window.MobileWarning.show();
                        }
                    }
                })
                .catch((error) => {
                    console.error('Error in auth state change:', error);
                });
        });
    } else {
        currentUser = null;
        userCompany = null;
        employeeCount = 0;
        console.log('User is signed out');
        window.location.href = 'index.html';
    }
});

async function checkPendingAssessments() {
    if (!currentUser || !userCompany) {
        console.error('User not logged in or company not set');
        return;
    }

    const companyRef = db.collection('companies').doc(userCompany);
    const usersRef = companyRef.collection('users');

    try {
        // Changed from 2 minutes to 2 hours
        const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
        const snapshot = await usersRef.where('status', '==', 'started').where('createdAt', '<=', twoHoursAgo).get();

        snapshot.forEach(async (doc) => {
            try {
                await doc.ref.update({
                    status: 'aborted',
                    abortedAt: firebase.firestore.FieldValue.serverTimestamp()
                });
                console.log(`Updated status to aborted for user: ${doc.id}`);
            } catch (updateError) {
                console.error(`Error updating status for user ${doc.id}:`, updateError);
            }
        });
    } catch (error) {
        console.error('Error checking pending assessments:', error);
    }
}

let pendingAssessmentInterval = null;

function startPendingAssessmentCheck() {
    if (pendingAssessmentInterval) {
        clearInterval(pendingAssessmentInterval);
    }
    pendingAssessmentInterval = setInterval(checkPendingAssessments, 15 * 60 * 1000);
    checkPendingAssessments();
}

function stopPendingAssessmentCheck() {
    if (pendingAssessmentInterval) {
        clearInterval(pendingAssessmentInterval);
        pendingAssessmentInterval = null;
    }
}


async function loadMainContent() {
    const mainContent = document.querySelector('#main-content');
    if (!currentUser || !userCompany) {
        window.location.href = 'index.html';
        return;
    }

    showLoadingOverlay();

    try {
        const companyRef = db.collection('companies').doc(userCompany);
        const querySnapshot = await companyRef.collection('users').get();
        employeeCount = querySnapshot.size;

        updateNavigationState();

        let contentType;
        if (employeeCount < 1) {
            contentType = await PageLoader.loadInvitePage(mainContent, userCompany);
            updateActiveNavLink('invitations');
        } else {
            contentType = await PageLoader.loadDashboardPage(mainContent, userCompany, updateActiveNavLink);
        }

        currentContent = contentType;
        pushToNavigationState(contentType);

    } catch (error) {
        console.error('Error in loadMainContent:', error);
        const contentType = await PageLoader.loadInvitePage(mainContent, userCompany);
        updateActiveNavLink('invitations');
    } finally {
        hideLoadingOverlay();
    }
}



function setupEmployeeCountListener() {
    if (!userCompany) {
        console.error('User company not set');
        return;
    }

    const companyRef = db.collection('companies').doc(userCompany);
    companyRef.collection('users').onSnapshot((snapshot) => {
        const newCount = snapshot.size;
        console.log('Employee count updated:', newCount);

        if (newCount !== employeeCount) {
            employeeCount = newCount;
            updateNavigationState();

            // Only show unlock notification when crossing the threshold
            if (newCount >= 1 && employeeCount < 1) {
                showToast('Dashboard and Assessments features are now available!');
            }
        }
    }, (error) => {
        console.error('Error in employee count listener:', error);
    });
}

async function verifyEmployeeCount() {
    if (!userCompany) return 0;

    try {
        const snapshot = await db.collection('companies')
            .doc(userCompany)
            .collection('users')
            .get();

        return snapshot.size;
    } catch (error) {
        console.error('Error verifying employee count:', error);
        return 0;
    }
}


async function loadAssessmentsPage(mainContent) {
    // Check subscription access
    if (window.SubscriptionCheck) {
        const hasAccess = await window.SubscriptionCheck.checkFeatureAccess('Assessments');
        if (!hasAccess) {
            return; // User was shown access modal and chose not to subscribe
        }
    }

    PageLoader.loadAssessmentsPage(mainContent, userCompany)
        .then(contentType => {
            currentContent = contentType;
            pushToNavigationState(contentType);
            updateActiveNavLink(contentType);
        })
        .catch(error => {
            console.error('Error loading assessments page:', error);
        });
}

async function loadDashboardPage(mainContent) {
    // Check subscription access
    if (window.SubscriptionCheck) {
        const hasAccess = await window.SubscriptionCheck.checkFeatureAccess('Dashboard');
        if (!hasAccess) {
            return; // User was shown access modal and chose not to subscribe
        }
    }

    PageLoader.loadDashboardPage(mainContent, userCompany, updateActiveNavLink)
        .then(contentType => {
            currentContent = contentType;
            pushToNavigationState(contentType);
            // updateActiveNavLink is now called within loadDashboardPage
        })
        .catch(error => {
            console.error('Error loading dashboard page:', error);
        });
}

async function loadLearningPathsPage(mainContent) {
    // Check subscription access
    if (window.SubscriptionCheck) {
        const hasAccess = await window.SubscriptionCheck.checkFeatureAccess('Learning Paths');
        if (!hasAccess) {
            return; // User was shown access modal and chose not to subscribe
        }
    }

    PageLoader.loadLearningPathsPage(mainContent)
        .then(contentType => {
            currentContent = contentType;
            pushToNavigationState(contentType);
            updateActiveNavLink('learning paths');
        })
        .catch(error => {
            console.error('Error loading learning paths page:', error);
        });
}

function loadInvitePage(mainContent) {
    PageLoader.loadInvitePage(mainContent, userCompany)
        .then(contentType => {
            currentContent = contentType;
            pushToHistory(contentType);
            updateActiveNavLink('invitations');
        })
        .catch(error => {
            console.error('Error loading invite page:', error);
        });
}


async function loadReportsPage(mainContent) {
    // Check subscription access
    if (window.SubscriptionCheck) {
        const hasAccess = await window.SubscriptionCheck.checkFeatureAccess('Metrics');
        if (!hasAccess) {
            return; // User was shown access modal and chose not to subscribe
        }
    }

    PageLoader.loadReportsPage(mainContent, userCompany)
        .then(contentType => {
            currentContent = contentType;
            pushToNavigationState(contentType);
            updateActiveNavLink(contentType);
        })
        .catch(error => {
            console.error('Error loading reports page:', error);
        });
}

function loadDetailContent(mainContent, category) {
    PageLoader.loadDetailContent(mainContent, category, userCompany)
        .then(contentType => {
            currentContent = contentType;
            pushToNavigationState(contentType, category);
        })
        .catch(error => {
            console.error('Error loading detail content:', error);
        });
}

  function initializeUserMenu() {
    console.log('Initializing user menu');
    // The new user menu implementation is in user-menu.js
    // This function is kept for backward compatibility
  }

// These functions have been moved to user-menu.js
function setupCreditListener() {
    console.log('Credit listener is now handled in user-menu.js');
}

function populateUserInfo(user, userMenu) {
    console.log('User info population is now handled in user-menu.js');
}

// These helper functions have been moved to user-menu.js
function getPlanNameFromCredits(credits) {
    console.log('Plan name calculation is now handled in user-menu.js');
    return 'Free Trial';
}

function getDaysLeftInTrial(adminData) {
    console.log('Trial days calculation is now handled in user-menu.js');
    return 'Unknown time';
}

function getDaysUntilSubscriptionEnds(endDate) {
    console.log('Subscription end days calculation is now handled in user-menu.js');
    return null;
}

function setDefaultUserInfo(userMenu) {
    console.log('Default user info setting is now handled in user-menu.js');
}


// Make sure to call this function when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeUserMenu);


function initializeNotificationButton() {
    const notificationButton = document.querySelector('button[title="Notifications"]');
    if (!notificationButton) {
        console.error('Notification button not found');
        return;
    }

    const notificationWindow = document.createElement('div');
    notificationWindow.id = 'notification-window';
    notificationWindow.classList.add('notification-window', 'hidden');
    notificationWindow.innerHTML = `
        <div class="notification-content">
            <img src="checked.png" alt="All caught up" class="notification-icon">
            <p>All caught up!</p>
        </div>
    `;
    document.body.appendChild(notificationWindow);

    notificationButton.addEventListener('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        notificationWindow.classList.toggle('hidden');
        positionNotificationWindow(notificationButton, notificationWindow);
    });

    document.addEventListener('click', function(event) {
        if (!notificationWindow.contains(event.target) && event.target !== notificationButton) {
            notificationWindow.classList.add('hidden');
        }
    });
}

function positionNotificationWindow(button, window) {
    const buttonRect = button.getBoundingClientRect();
    const windowRect = window.getBoundingClientRect();

    window.style.top = `${buttonRect.bottom + 5}px`; // Add a small gap of 5px
    window.style.left = `${buttonRect.left + (buttonRect.width / 2) - (windowRect.width / 2)}px`;
}


// User menu initialization is now handled in user-menu.js

function initializeNavigationDrawer() {
    const navigationDrawer = document.getElementById('navigation-drawer');
    const openDrawerButton = document.getElementById('open-drawer');
    const closeDrawerButton = document.getElementById('close-drawer');
    const backdrop = document.getElementById('backdrop');
    const logoImage = document.getElementById('logo-image');

    // Remove existing event listeners
    const newOpenDrawerButton = openDrawerButton.cloneNode(true);
    openDrawerButton.parentNode.replaceChild(newOpenDrawerButton, openDrawerButton);

    const newCloseDrawerButton = closeDrawerButton.cloneNode(true);
    closeDrawerButton.parentNode.replaceChild(newCloseDrawerButton, closeDrawerButton);

    const newBackdrop = backdrop.cloneNode(true);
    backdrop.parentNode.replaceChild(newBackdrop, backdrop);

    newOpenDrawerButton.addEventListener('click', () => {
        navigationDrawer.classList.remove('-translate-x-full');
        navigationDrawer.classList.add('translate-x-0');
        newBackdrop.classList.remove('hidden');
    });

    newCloseDrawerButton.addEventListener('click', () => {
        navigationDrawer.classList.remove('translate-x-0');
        navigationDrawer.classList.add('-translate-x-full');
        newBackdrop.classList.add('hidden');
    });

    newBackdrop.addEventListener('click', () => {
        navigationDrawer.classList.remove('translate-x-0');
        navigationDrawer.classList.add('-translate-x-full');
        newBackdrop.classList.add('hidden');
    });


    if (logoImage) {
        logoImage.addEventListener('click', async () => {
            // Check subscription status before loading dashboard
            if (window.SubscriptionCheck) {
                const hasSubscription = await window.SubscriptionCheck.checkAccess();
                if (!hasSubscription) {
                    // Show feature access modal
                    if (window.FeatureAccessModal) {
                        await window.FeatureAccessModal.show({
                            title: 'Subscription Required',
                            message: 'Access to Dashboard requires an active subscription. Would you like to select a subscription plan now?',
                            confirmText: 'Select Subscription',
                            cancelText: 'Not Now',
                            featureName: 'Dashboard',
                            redirectToInvite: true
                        });
                    }
                    // After modal is closed, update active nav link to invitations
                    updateActiveNavLink('invitations');
                    return;
                }
            }

            if (employeeCount === 0) {
                return;
            }
            const mainContent = document.querySelector('#main-content');
            loadDashboardPage(mainContent);
        });
        logoImage.style.cursor = 'pointer';
    }


    const allNavLinks = document.querySelectorAll('#navigation-drawer nav a, header nav a');
    allNavLinks.forEach(link => {
        const newLink = link.cloneNode(true);
        link.parentNode.replaceChild(newLink, link);

        newLink.addEventListener('click', async (event) => {
            const linkText = newLink.textContent.trim().toLowerCase();
            event.preventDefault();

            // Always check subscription status first (except for invitations page)
            if (linkText !== 'invitations' && window.SubscriptionCheck) {
                const hasSubscription = await window.SubscriptionCheck.checkAccess();
                if (!hasSubscription) {
                    // Show feature access modal for restricted pages
                    const featureName = linkText.charAt(0).toUpperCase() + linkText.slice(1);
                    if (window.FeatureAccessModal) {
                        await window.FeatureAccessModal.show({
                            title: 'Subscription Required',
                            message: `Access to ${featureName} requires an active subscription. Would you like to select a subscription plan now?`,
                            confirmText: 'Select Subscription',
                            cancelText: 'Not Now',
                            featureName: featureName,
                            redirectToInvite: true
                        });
                    }
                    // After modal is closed, update active nav link to invitations
                    updateActiveNavLink('invitations');
                    return;
                }
            }

            // Continue with normal navigation if user has subscription or is accessing invitations
            if (employeeCount === 0 && (linkText === 'dashboard' || linkText === 'assessments')) {
                // Load the welcome page instead
                const mainContent = document.querySelector('#main-content');
                loadWelcomePage(mainContent);
                updateActiveNavLink(linkText);
                return;
            }

            allNavLinks.forEach(l => l.classList.remove('active'));
            newLink.classList.add('active');

            if (currentContent === linkText) {
                return;
            }

            const mainContent = document.querySelector('#main-content');
            showLoadingOverlay();

            setTimeout(() => {
                switch (linkText) {
                    case 'assessments':
                        loadAssessmentsPage(mainContent);
                        break;
                    case 'dashboard':
                        loadDashboardPage(mainContent);
                        break;
                    case 'learning paths':
                        loadLearningPathsPage(mainContent);
                        break;
                    case 'invitations':
                        loadInvitePage(mainContent);
                        break;
                    case 'reports':
                        loadReportsPage(mainContent);
                        break;
                    default:
                        mainContent.innerHTML = '<p>No content available for this link.</p>';
                        currentContent = 'default';
                }
                currentContent = linkText;

                // Close the mobile navigation drawer if it's open
                if (window.innerWidth < 768) {
                    navigationDrawer.classList.remove('translate-x-0');
                    navigationDrawer.classList.add('-translate-x-full');
                    newBackdrop.classList.add('hidden');
                }
            }, 300);
        });

    });

    document.removeEventListener('dashboardCardClicked', handleDashboardCardClick);

    // Add event listener for dashboard card clicks
    document.addEventListener('dashboardCardClicked', handleDashboardCardClick);

    // Function to update nav links based on employeeCount
    function updateNavLinksState() {
        allNavLinks.forEach(link => {
            const linkText = link.textContent.trim().toLowerCase();
            if (employeeCount === 0 && (linkText === 'dashboard' || linkText === 'assessments')) {
                link.classList.add('pointer-events-none', 'opacity-50');
            } else {
                link.classList.remove('pointer-events-none', 'opacity-50');
            }
        });
    }

    updateNavLinksState();
}


function handleDashboardCardClick(event) {
    const category = event.detail.category;
    const mainContent = document.querySelector('#main-content');
    loadLearningPathsPage(mainContent, category);
    updateActiveNavLink('learning paths');
}



function updateActiveNavLink(linkText) {
    const allNavLinks = document.querySelectorAll('#navigation-drawer nav a, header nav a');
    allNavLinks.forEach(link => {
        // Normalize linkText and current link text for comparison
        const currentLinkText = link.textContent.trim().toLowerCase();
        const normalizedLinkText = linkText.toLowerCase();

        // Check if it's either a direct match or a metrics/reports match
        const isMatch = currentLinkText === normalizedLinkText ||
                       (currentLinkText === 'metrics' && normalizedLinkText === 'reports') ||
                       (currentLinkText === 'reports' && normalizedLinkText === 'metrics');

        if (isMatch) {
            link.classList.add('active');
            link.setAttribute('aria-current', 'page');
        } else {
            link.classList.remove('active');
            link.removeAttribute('aria-current');
        }
    });
}

// New function to set up all event listeners for the invite page
function setupInviteEventListeners() {
    const sendInvitationsButton = document.getElementById('sendInvitationsButton');
    if (sendInvitationsButton) {
        sendInvitationsButton.addEventListener('click', sendInvitations);
    } else {
        console.error('Send Invitations button not found');
    }
}



function loadContent(url, contentType) {
    showLoadingOverlay();
    const mainContent = document.querySelector('#main-content');
    mainContent.style.opacity = 1;
    mainContent.style.transition = 'opacity 0.3s ease-in-out';
    mainContent.style.opacity = 0;
    mainContent.addEventListener('transitionend', function onFadeOut(event) {
        if (event.propertyName === 'opacity') {
            mainContent.removeEventListener('transitionend', onFadeOut);
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(data => {
                    removeExistingScript(contentType);

                    mainContent.innerHTML = data;
                    mainContent.style.opacity = 0;
                    void mainContent.offsetWidth;
                    mainContent.style.opacity = 1;
                    loadScript(`${contentType}.js`, () => {
                        console.log(`${contentType}.js loaded`);
                        if (typeof window[`initialize${capitalize(contentType)}`] === 'function') {
                            window[`initialize${capitalize(contentType)}`](userCompany);
                        }

                        updateActiveNavLink(contentType);
                        currentContent = contentType;
                        pushToHistory(contentType);
                        setTimeout(hideLoadingOverlay, 300);
                    });
                })
                .catch(error => {
                    console.error(`Error loading ${url}:`, error);
                    hideLoadingOverlay();
                    mainContent.style.opacity = 1;
                });
        }
    });
}


function removeExistingScript(contentType) {
    const existingScript = document.querySelector(`script[src="${contentType}.js"]`);
    if (existingScript) {
        existingScript.remove();
    }
}

function loadScript(src, callback) {
    const script = document.createElement('script');
    script.src = src;
    script.onload = callback;
    script.onerror = () => console.error(`Failed to load ${src}`);
    document.body.appendChild(script);
}

function capitalize(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

function pushToHistory(page, category = null) {
    navigationStack.push({ page, category });
    const stateObj = { page, category };
    history.pushState(stateObj, '', `#${page}`);
}
function popFromHistory() {
    if (pageHistory.length > 1) {
        pageHistory.pop(); // Remove current page
        return pageHistory[pageHistory.length - 1]; // Return previous page
    }
    return 'dashboard'; // Default to dashboard if no history
}

async function handlePopState(event) {
    console.log('Handling popstate event', event.state);
    if (event.state && typeof event.state.stateIndex !== 'undefined') {
        // Don't allow navigation to duplicate enrollment states
        if (navigationState.stack[event.state.stateIndex].page === 'enrollment' &&
            navigationState.currentIndex > 0 &&
            navigationState.stack[navigationState.currentIndex].page === 'enrollment') {
            // Skip the duplicate enrollment state
            history.back();
            return;
        }

        navigationState.currentIndex = event.state.stateIndex;
        const currentState = navigationState.stack[navigationState.currentIndex];

        // Always allow navigation to invite page
        if (currentState.page === 'invite') {
            loadInvitePage(getMainContent());
            return;
        }

        // Check subscription status for all other pages
        if (window.SubscriptionCheck && currentState.page !== 'invite') {
            const hasSubscription = await window.SubscriptionCheck.checkAccess();
            if (!hasSubscription) {
                // Show feature access modal
                if (window.FeatureAccessModal) {
                    const featureName = currentState.page.charAt(0).toUpperCase() + currentState.page.slice(1);
                    await window.FeatureAccessModal.show({
                        title: 'Subscription Required',
                        message: `Access to ${featureName} requires an active subscription. Would you like to select a subscription plan now?`,
                        confirmText: 'Select Subscription',
                        cancelText: 'Not Now',
                        featureName: featureName,
                        redirectToInvite: true
                    });
                }
                // After modal is closed, load invite page and update URL
                loadInvitePage(getMainContent());
                updateActiveNavLink('invitations');
                history.replaceState({ stateIndex: navigationState.currentIndex }, '', '#invite');
                return;
            }
        }

        loadContentBasedOnState(currentState.page, currentState.category, currentState.params);
    } else {
        // If there's no state, check subscription before going to dashboard
        if (window.SubscriptionCheck) {
            const hasSubscription = await window.SubscriptionCheck.checkAccess();
            if (!hasSubscription) {
                // Show feature access modal
                if (window.FeatureAccessModal) {
                    await window.FeatureAccessModal.show({
                        title: 'Subscription Required',
                        message: 'Access to Dashboard requires an active subscription. Would you like to select a subscription plan now?',
                        confirmText: 'Select Subscription',
                        cancelText: 'Not Now',
                        featureName: 'Dashboard',
                        redirectToInvite: true
                    });
                }
                // After modal is closed, load invite page and update navigation state
                navigationState.currentIndex = 0;
                navigationState.stack = [{ page: 'invite', category: null, params: {} }];
                loadInvitePage(getMainContent());
                updateActiveNavLink('invitations');
                history.replaceState({ stateIndex: 0 }, '', '#invite');
                return;
            }
        }

        // If user has subscription, go to dashboard
        navigationState.currentIndex = 0;
        navigationState.stack = [{ page: 'dashboard', category: null, params: {} }];
        loadDashboardPage(getMainContent());
    }
}

async function loadContentBasedOnState(page, category = null, params = {}) {
    const mainContent = getMainContent();
    console.log('Loading content based on state:', { page, category, params });

    // Always allow access to invite page
    if (page === 'invite') {
        loadInvitePage(mainContent);
        return;
    }

    // Check subscription status for all other pages
    if (window.SubscriptionCheck && page !== 'invite') {
        const hasSubscription = await window.SubscriptionCheck.checkAccess();
        if (!hasSubscription) {
            // Show feature access modal
            if (window.FeatureAccessModal) {
                const featureName = page.charAt(0).toUpperCase() + page.slice(1);
                await window.FeatureAccessModal.show({
                    title: 'Subscription Required',
                    message: `Access to ${featureName} requires an active subscription. Would you like to select a subscription plan now?`,
                    confirmText: 'Select Subscription',
                    cancelText: 'Not Now',
                    featureName: featureName,
                    redirectToInvite: true
                });
            }
            // After modal is closed, load invite page
            loadInvitePage(mainContent);
            updateActiveNavLink('invitations');
            return;
        }
    }

    // Continue with normal navigation if user has subscription
    switch (page) {
        case 'dashboard':
            loadDashboardPage(mainContent);
            break;
        case 'learningpaths':
            loadLearningPathsPage(mainContent, category);
            break;
        case 'detail':
            loadDetailContent(mainContent, category);
            break;
        case 'enrollment':
            loadEnrollmentPage(mainContent, category, params);
            break;
        case 'invite':
            loadInvitePage(mainContent);
            break;
        default:
            console.warn('Unknown page type:', page);
            loadDashboardPage(mainContent);
    }
}



function handleBackNavigation() {
    if (navigationStack.length > 1) {
        navigationStack.pop(); // Remove current page
        const previousPage = navigationStack[navigationStack.length - 1];
        loadContentBasedOnState(previousPage.page, previousPage.category);
    } else {
        loadContentBasedOnState('dashboard');
    }
}

function pushToNavigationState(page, category = null, params = {}) {
    console.log('Pushing to navigation state:', { page, category, params });

    // Don't push duplicate states
    if (navigationState.stack.length > 0) {
        const currentState = navigationState.stack[navigationState.currentIndex];
        if (currentState.page === page &&
            currentState.category === category &&
            JSON.stringify(currentState.params) === JSON.stringify(params)) {
            console.log('State is the same, not pushing');
            return;
        }
    }

    // Clear forward history if navigating from middle of stack
    if (navigationState.currentIndex < navigationState.stack.length - 1) {
        navigationState.stack = navigationState.stack.slice(0, navigationState.currentIndex + 1);
    }

    navigationState.stack.push({ page, category, params });
    navigationState.currentIndex = navigationState.stack.length - 1;

    const url = new URL(window.location);
    url.hash = `#${page}${category ? '/' + category : ''}`;

    // Replace state if it's enrollment page and previous state was also enrollment
    const shouldReplace = page === 'enrollment' &&
                         navigationState.stack.length > 1 &&
                         navigationState.stack[navigationState.stack.length - 2].page === 'enrollment';

    if (shouldReplace) {
        history.replaceState({ stateIndex: navigationState.currentIndex }, '', url);
    } else {
        history.pushState({ stateIndex: navigationState.currentIndex }, '', url);
    }

    console.log('Navigation stack:', navigationState.stack);
}

async function navigateBack() {
    if (navigationState.currentIndex > 0) {
        let targetIndex = navigationState.currentIndex - 1;

        // Skip duplicate enrollment states when going back
        while (targetIndex > 0 &&
               navigationState.stack[targetIndex].page === 'enrollment' &&
               navigationState.stack[navigationState.currentIndex].page === 'enrollment') {
            targetIndex--;
        }

        navigationState.currentIndex = targetIndex;
        const previousState = navigationState.stack[navigationState.currentIndex];

        // Always allow navigation to invite page
        if (previousState.page === 'invite') {
            loadInvitePage(getMainContent());
            return;
        }

        // Check subscription status for all other pages
        if (window.SubscriptionCheck && previousState.page !== 'invite') {
            const hasSubscription = await window.SubscriptionCheck.checkAccess();
            if (!hasSubscription) {
                // Show feature access modal
                if (window.FeatureAccessModal) {
                    const featureName = previousState.page.charAt(0).toUpperCase() + previousState.page.slice(1);
                    await window.FeatureAccessModal.show({
                        title: 'Subscription Required',
                        message: `Access to ${featureName} requires an active subscription. Would you like to select a subscription plan now?`,
                        confirmText: 'Select Subscription',
                        cancelText: 'Not Now',
                        featureName: featureName,
                        redirectToInvite: true
                    });
                }
                // After modal is closed, load invite page
                loadInvitePage(getMainContent());
                updateActiveNavLink('invitations');
                return;
            }
        }

        // Check if we're coming from assessments or reports
        if (previousState.page === 'assessments' || previousState.page === 'reports') {
            const mainContent = getMainContent();
            if (previousState.page === 'assessments') {
                loadAssessmentsPage(mainContent);
            } else {
                loadReportsPage(mainContent);
            }
        } else {
            loadContentBasedOnState(previousState.page, previousState.category, previousState.params);
        }
    } else {
        // Check subscription before loading dashboard
        if (window.SubscriptionCheck) {
            const hasSubscription = await window.SubscriptionCheck.checkAccess();
            if (!hasSubscription) {
                // Show feature access modal
                if (window.FeatureAccessModal) {
                    await window.FeatureAccessModal.show({
                        title: 'Subscription Required',
                        message: 'Access to Dashboard requires an active subscription. Would you like to select a subscription plan now?',
                        confirmText: 'Select Subscription',
                        cancelText: 'Not Now',
                        featureName: 'Dashboard',
                        redirectToInvite: true
                    });
                }
                // After modal is closed, load invite page
                loadInvitePage(getMainContent());
                updateActiveNavLink('invitations');
                return;
            }
        }

        loadDashboardPage(getMainContent());
    }
}

function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded shadow-lg z-50 animate-fade-in';

    // Add brand styling to toast
    toast.style.backgroundColor = '#1547bb';
    toast.style.color = 'white';
    toast.style.borderRadius = '0.375rem';
    toast.style.boxShadow = '0 4px 6px -1px rgba(18, 28, 65, 0.1), 0 2px 4px -1px rgba(18, 28, 65, 0.06)';

    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('animate-fade-out');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

async function initializeDashboardTour(isManualStart = false) {
    if (tourInitialized) {
        console.log('Tour already initialized, skipping');
        return;
    }

    console.log('Initializing dashboard tour, manual start:', isManualStart);
    try {
        await loadIntroJS();

        const user = firebase.auth().currentUser;
        if (!user) {
            console.log('No user logged in, skipping tour');
            return;
        }

        // Only check tour completion status for automatic starts
        if (!isManualStart) {
            const adminDoc = await db.collection('Admins').doc(user.email).get();
            const adminData = adminDoc.data();

            if (adminData.hasCompletedTour) {
                console.log('Tour already completed, skipping initialization');
                return;
            }
        }

        tourInitialized = true;

        const intro = introJs();

        // Different tour steps based on employee count
        const newUserSteps = [
            {
                title: 'Welcome to Skills Assess dashboard!',
                intro: "Let's get you started with setting up your organisation's learning journey."
            },
            {
                element: 'a[data-content="invite"]',
                title: 'Start Here: Invitations',
                intro: 'This is where you begin! Use this section to invite your team members to take assessments. You can invite individuals or bulk import users.',
                position: 'right'
            },
            {
                element: 'a[data-content="dashboard"]',
                title: 'Dashboard',
                intro: 'The dashboard will be activated once at least 1 team member has started their assessment. Here you\'ll see completion rates, performance metrics, and key insights.',
                position: 'right'
            },
            {
                element: 'a[data-content="assessments"]',
                title: 'Assessments',
                intro: 'The assessments section will also become available once your team starts participating. You\'ll be able to track progress and view detailed results here.',
                position: 'right'
            },
            {
                element: 'a[data-content="learning-paths"]',
                title: 'Learning Paths 🎓',
                intro: 'Browse available learning paths that your team can pursue. These structured programs help develop specific skills and competencies.',
                position: 'right'
            },
            {
                element: 'a[data-content="reports"]',
                title: 'Metrics',
                intro: 'Access detailed analytics and insights about your team\'s assessment performance, skill gaps, and learning progress. This will be available once your team starts participating.',
                position: 'right'
            },
            {
                title: "Let's Get Started!",
                intro: 'Your first step is to invite team members. Head to the Invitations section to begin. Once 1 or more people start taking assessments, you\'ll get access to the dashboard, assessments, and metrics features.'
            }
        ];

        const regularUserSteps = [
            {
                title: 'Welcome to Skills Assess dashboard!',
                intro: "Let's take a quick tour of your dashboard and its features."
            },
            {
                element: 'a[data-content="dashboard"]',
                title: 'Dashboard',
                intro: 'This is your main dashboard where you can see an overview of all assessments, completion rates, and key metrics at a glance.',
                position: 'right'
            },
            {
                element: 'a[data-content="assessments"]',
                title: 'Assessments',
                intro: 'Access all your company assessments here. You can view ongoing assessments, completed ones, and track individual progress.',
                position: 'right'
            },
            {
                element: 'a[data-content="learning-paths"]',
                title: 'Learning Paths',
                intro: 'Explore structured learning paths designed to help your employees develop specific skills and competencies.',
                position: 'right'
            },
            {
                element: 'a[data-content="reports"]',
                title: 'Metrics',
                intro: 'Access detailed analytics and insights about your team\'s assessment performance, skill gaps, and learning progress.',
                position: 'right'
            },
            {
                element: 'a[data-content="invite"]',
                title: 'Invitations',
                intro: 'Easily invite new employees to take assessments. You can send individual invites or bulk import users.',
                position: 'right'
            },
            {
                element: '#user-menu-button',
                title: 'User Menu',
                intro: 'Access your profile settings, company information, and logout option here.',
                position: 'left'
            }
        ];

        intro.setOptions({
            steps: employeeCount < 5 ? newUserSteps : regularUserSteps,
            showProgress: true,
            showBullets: true,
            exitOnOverlayClick: false,
            overlayOpacity: 0.8,
            tooltipClass: 'customTooltip',
            prevLabel: '← Previous',
            nextLabel: 'Next →',
            doneLabel: employeeCount < 5 ? 'Start Inviting!' : 'Get Started!',
            positionPrecedence: ['bottom', 'top', 'right', 'left'],
            scrollToElement: true
        });

        const style = document.createElement('style');
        style.textContent = `
          .introjs-tooltip {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(18, 28, 65, 0.15);
                max-width: 350px;
                width: 90vw;
                z-index: 999999 !important;
            }

            /* Special styling for welcome step tooltip */
            .introjs-tooltip[data-step="0"] {
                position: fixed !important;
                left: 50% !important;
                top: 50% !important;
                transform: translate(-50%, -50%) !important;
                margin: 0 !important;
            }

            /* Remove all default introjs button styling first */
            .introjs-button {
                text-shadow: none !important;
                background-image: none !important;
                background-color: #1547bb !important;
                color: white !important;
                border: none !important;
                border-radius: 4px !important;
                box-shadow: none !important;
                padding: 8px 16px !important;
                margin: 4px !important;
                font-size: 14px !important;
                font-weight: 500 !important;
            }

            /* Explicitly style each button */
            .introjs-prevbutton,
            .introjs-nextbutton,
            .introjs-donebutton {
                background: #1547bb !important;
                color: #ffffff !important;
                border: none !important;
                text-shadow: none !important;
            }

            /* Hover state */
            .introjs-button:hover {
                background-color: #121c41 !important;
                color: #ffffff !important;
            }

            /* Disabled state */
            .introjs-disabled,
            .introjs-disabled:hover,
            .introjs-disabled:focus {
                color: #ffffff !important;
                opacity: 0.5 !important;
                cursor: not-allowed !important;
                background-color: #1547bb !important;
            }

            /* Other introjs styling */
            .introjs-floating {
                position: fixed !important;
                margin-top: -10px;
            }

            .introjs-fixedTooltip {
                position: fixed !important;
            }

            .introjs-overlay {
                z-index: 999998 !important;
                position: fixed !important;
            }

            .introjs-helperLayer {
                z-index: 999997 !important;s
                position: fixed !important;
            }

            .introjs-tooltipReferenceLayer {
                z-index: 999999 !important;
                position: fixed !important;
            }

            .introjs-helperNumberLayer {
                z-index: 999999 !important;
            }

            .introjs-tooltiptext {
                color: #121c41;
                font-size: 14px;
                line-height: 1.6;
                padding: 20px;
            }

            .introjs-arrow {
                display: none !important;
            }

            .introjs-tooltipbuttons {
                padding: 12px;
                border-top: 1px solid #e5e7eb;
                text-align: right !important;
            }

            .disabled-nav-link {
                opacity: 0.5;
                cursor: not-allowed;
                pointer-events: none;
            }

            .feature-locked-badge {
                font-size: 10px;
                background-color: #EF4444;
                color: white;
                padding: 2px 6px;
                border-radius: 9999px;
                margin-left: 8px;
            }

            .introjs-tooltiptitle {
                color: #1547bb;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 8px;
            }
        `;
        document.head.appendChild(style);

        // Add a script to ensure button styling is applied after intro.js loads
        const fixButtonsScript = document.createElement('script');
        fixButtonsScript.textContent = `
            function fixIntroJsButtons() {
                const buttons = document.querySelectorAll('.introjs-button');
                buttons.forEach(button => {
                    button.style.backgroundColor = '#1547bb';
                    button.style.color = '#ffffff';
                    button.style.border = 'none';
                    button.style.boxShadow = 'none';
                    button.style.textShadow = 'none';
                    button.style.padding = '8px 16px';
                });
            }

            // Run initially and set up a mutation observer to catch new buttons
            setTimeout(fixIntroJsButtons, 100);

            // Create a mutation observer to watch for new buttons
            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        for (let node of mutation.addedNodes) {
                            if (node.classList && node.classList.contains('introjs-button')) {
                                fixIntroJsButtons();
                            }
                        }
                    }
                });
            });

            // Start observing the document with the configured parameters
            observer.observe(document.body, { childList: true, subtree: true });
        `;
        document.head.appendChild(fixButtonsScript);

        // Handle tour completion
        intro.oncomplete(async function() {
            try {
                if (user && !isManualStart) {
                    await db.collection('Admins').doc(user.email).update({
                        hasCompletedTour: true,
                        tourCompletedAt: firebase.firestore.FieldValue.serverTimestamp(),
                        tourCompletionStatus: 'completed'
                    });
                }

                if (employeeCount < 5) {
                    const mainContent = document.querySelector('#main-content');
                    loadInvitePage(mainContent);
                }
            } catch (error) {
                console.error('Error updating tour completion status:', error);
            } finally {
                tourInitialized = false;
            }
        });

        // Handle tour exit
        intro.onexit(async function() {
            try {
                if (user && !isManualStart) {
                    await db.collection('Admins').doc(user.email).update({
                        hasCompletedTour: true,
                        tourCompletedAt: firebase.firestore.FieldValue.serverTimestamp(),
                        tourCompletionStatus: 'exited'
                    });
                }
            } catch (error) {
                console.error('Error updating tour exit status:', error);
            } finally {
                tourInitialized = false;
            }
        });

        intro.start();

    } catch (error) {
        console.error('Failed to initialize tour:', error);
        tourInitialized = false;
        alert('Failed to load the tour. Please try again later.');
    }
}

function addHelpButton() {
    const header = document.querySelector('header nav');
    if (header && !document.querySelector('#tour-help-button')) {
        const helpButton = document.createElement('button');
        helpButton.id = 'tour-help-button';
        helpButton.className = 'ml-4 p-2 text-gray-500 hover:text-gray-700';
        helpButton.title = 'Start Tour';
        helpButton.innerHTML = `
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
        `;
        helpButton.onclick = () => {
            tourInitialized = false;
            initializeDashboardTour(true);
        };
        header.appendChild(helpButton);
    }
}


function updateNavigationState() {
    const dashboardLinks = document.querySelectorAll('a[data-content="dashboard"]');
    const assessmentsLinks = document.querySelectorAll('a[data-content="assessments"]');
    const reportsLinks = document.querySelectorAll('a[data-content="reports"]');

    if (dashboardLinks.length === 0 || assessmentsLinks.length === 0 || reportsLinks.length === 0) {
        console.warn('Navigation links not found, retrying...');
        setTimeout(updateNavigationState, 100);
        return;
    }    // Verify employee count if not in demo mode
    if (!window.isDemoMode && userCompany) {
        db.collection('companies').doc(userCompany).collection('users').get()
            .then(snapshot => {
                employeeCount = snapshot.size;
                setupNavigationLinks();
            })
            .catch(error => {
                console.error('Error verifying employee count:', error);
                setupNavigationLinks();
            });
    } else {
        setupNavigationLinks();
    }

    function setupNavigationLinks() {
        const setupLinkBehavior = (links, feature) => {
            links.forEach(link => {
                // Always enable links in demo mode
                if (employeeCount < 1 && !window.isDemoMode &&
                    (feature === 'dashboard' || feature === 'assessments' || feature === 'reports')) {
                    link.classList.add('disabled-nav-link');

                    if (!link.querySelector('.feature-locked-badge')) {
                        const badge = document.createElement('span');
                        badge.className = 'feature-locked-badge';
                        badge.textContent = 'Locked';
                        link.appendChild(badge);
                    }

                    const newLink = link.cloneNode(true);
                    link.parentNode.replaceChild(newLink, link);

                    newLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        showToast(`This feature will be unlocked after at least 1 member has completed the assessment (${employeeCount}/1)`);
                    });
                } else {
                    link.classList.remove('disabled-nav-link');
                    
                    // Remove any existing demo/locked badges first
                    const existingBadges = link.querySelectorAll('.feature-locked-badge');
                    existingBadges.forEach(badge => badge.remove());
                    
                    // Add demo badge if demo mode is active
                    if (window.isDemoMode) {
                        const badge = document.createElement('span');
                        badge.className = 'feature-locked-badge';
                        badge.textContent = 'Demo';
                        link.appendChild(badge);
                    }

                    link.style.pointerEvents = 'auto';
                    link.style.cursor = 'pointer';

                    const newLink = link.cloneNode(true);
                    link.parentNode.replaceChild(newLink, link);

                    newLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        const mainContent = document.querySelector('#main-content');
                        if (feature === 'dashboard') {
                            loadDashboardPage(mainContent);
                        } else if (feature === 'assessments') {
                            loadAssessmentsPage(mainContent);
                        } else if (feature === 'reports') {
                            loadReportsPage(mainContent);
                        }
                        updateActiveNavLink(feature);
                    });
                }
            });
        };

        setupLinkBehavior(dashboardLinks, 'dashboard');
        setupLinkBehavior(assessmentsLinks, 'assessments');
        setupLinkBehavior(reportsLinks, 'reports');
    }
}


function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded shadow-lg z-50 animate-fade-in';
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('animate-fade-out');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

document.addEventListener('DOMContentLoaded', function() {
    setupEmployeeCountListener();
    updateNavigationState();
    addHelpButton();
});

window.debugTourState = () => {
    const user = firebase.auth().currentUser;
    if (user) {
        verifyTourState(user).then(state => {
            console.log('Current tour state:', state);
        });
    }
};

function showNotification(message, type = 'success') {
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    const notificationContainer = document.createElement('div');
    notificationContainer.classList.add('notification');

    Object.assign(notificationContainer.style, {
        position: 'fixed',
        top: '70px',
        left: '50%',
        transform: 'translateX(-50%)',
        padding: '12px 24px',
        borderRadius: '6px',
        opacity: '0',
        transition: 'opacity 0.3s ease',
        zIndex: '9999',
        boxShadow: '0 2px 8px rgba(18, 28, 65, 0.15)',
        fontWeight: '500'
    });

    // Add type-specific styles with brand colors
    if (type === 'success') {
        Object.assign(notificationContainer.style, {
            backgroundColor: '#1547BB',
            color: 'white',
            borderLeft: '4px solid #0D3592'
        });
    } else if (type === 'error') {
        Object.assign(notificationContainer.style, {
            backgroundColor: '#FEE2E2',
            color: '#991B1B',
            borderLeft: '4px solid #DC2626'
        });
    } else if (type === 'info') {
        Object.assign(notificationContainer.style, {
            backgroundColor: '#E0F2FE',
            color: '#0C4A6E',
            borderLeft: '4px solid #0284C7'
        });
    } else if (type === 'warning') {
        Object.assign(notificationContainer.style, {
            backgroundColor: '#FEF3C7',
            color: '#92400E',
            borderLeft: '4px solid #F59E0B'
        });
    }

    notificationContainer.textContent = message;

    document.body.appendChild(notificationContainer);

    requestAnimationFrame(() => {
        notificationContainer.style.opacity = '1';
    });

    setTimeout(() => {
        notificationContainer.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(notificationContainer)) {
                notificationContainer.remove();
            }
        }, 300);
    }, 4000);
}

// Initialize subscription change detector
function initializeSubscriptionChangeDetector() {
    if (typeof window.subscriptionChangeDetectorInitialized !== 'undefined') {
        return;
    }
    
    window.subscriptionChangeDetectorInitialized = true;
    
    // Get URL parameters to check if we're returning from Stripe
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    const fromSuccess = urlParams.get('from') === 'success';
    
    // If returning from a successful checkout
    if (sessionId || fromSuccess) {
        setTimeout(() => {
            showNotification('Subscription update completed successfully!', 'success');
        }, 1000);
    }
    
    // Check if we're coming from topup success
    const isTopup = urlParams.get('topup') === 'true';
    if (isTopup && (sessionId || fromSuccess)) {
        setTimeout(() => {
            showNotification('Credit top-up completed successfully!', 'success');
        }, 1000);
    }
    
    // Listen for subscription changes through Firebase
    firebase.auth().onAuthStateChanged(function(user) {
        if (user) {
            // Initialize previous subscription state
            let previousSubscriptionState = null;
            
            // Set up real-time listener for subscription changes
            const db = firebase.firestore();
            db.collection('Admins').doc(user.email)
                .onSnapshot((doc) => {
                    if (doc.exists) {
                        const userData = doc.data();
                        
                        // Skip the first update since we're just initializing
                        if (previousSubscriptionState === null) {
                            previousSubscriptionState = {
                                subscriptionType: userData.subscriptionType,
                                subscriptionActive: userData.subscriptionActive,
                                credits: userData.credits,
                                subscriptionEndDate: userData.subscriptionEndDate ? 
                                  userData.subscriptionEndDate.toDate().getTime() : null
                            };
                            return;
                        }
                        
                        // Get current state for comparison
                        const currentState = {
                            subscriptionType: userData.subscriptionType,
                            subscriptionActive: userData.subscriptionActive,
                            credits: userData.credits,
                            subscriptionEndDate: userData.subscriptionEndDate ? 
                              userData.subscriptionEndDate.toDate().getTime() : null
                        };
                        
                        // Check for subscription type change
                        if (previousSubscriptionState.subscriptionType !== currentState.subscriptionType) {
                            if (currentState.subscriptionType === 'cancelled') {
                                showNotification('Your subscription has been cancelled.', 'info');
                            } else if (currentState.subscriptionType === 'expired') {
                                showNotification('Your subscription has expired.', 'warning');
                            } else if (previousSubscriptionState.subscriptionType && currentState.subscriptionType) {
                                // Check if this is an upgrade or downgrade
                                const planRank = {
                                    'freeTrial': 0,
                                    'Assess100': 1,
                                    'Assess250': 2,
                                    'Assess500': 3
                                };
                                
                                const prevRank = planRank[previousSubscriptionState.subscriptionType] || 0;
                                const currRank = planRank[currentState.subscriptionType] || 0;
                                
                                if (currRank > prevRank) {
                                    showNotification(`Your subscription has been upgraded to ${currentState.subscriptionType}.`, 'success');
                                } else if (currRank < prevRank) {
                                    showNotification(`Your subscription has been downgraded to ${currentState.subscriptionType}.`, 'info');
                                } else {
                                    showNotification(`Your subscription type has changed to ${currentState.subscriptionType}.`, 'info');
                                }
                            }
                        }
                        
                        // Check for credit changes (only notify for increases)
                        if (currentState.credits > previousSubscriptionState.credits) {
                            const difference = currentState.credits - previousSubscriptionState.credits;
                            showNotification(`${difference} credits have been added to your account.`, 'success');
                        }
                        
                        // Check for subscription end date changes
                        if (previousSubscriptionState.subscriptionEndDate !== currentState.subscriptionEndDate &&
                            previousSubscriptionState.subscriptionType === currentState.subscriptionType) {
                            // This could be a renewal
                            if (currentState.subscriptionEndDate > previousSubscriptionState.subscriptionEndDate) {
                                const newEndDate = new Date(currentState.subscriptionEndDate);
                                const formattedDate = newEndDate.toLocaleDateString('en-GB', {
                                    day: 'numeric', month: 'long', year: 'numeric'
                                });
                                showNotification(`Your subscription has been renewed until ${formattedDate}.`, 'success');
                            }
                        }
                        
                        // Update previous state for next comparison
                        previousSubscriptionState = currentState;
                    }
                });
        }
    });
}

// Call the initialization function when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    // ...existing code...
    initializeSubscriptionChangeDetector();
});

window.SkillsGapAnalysis = {
    async fetchData(email, company) {
        try {
            if (!email || !company) {
                throw new Error('Missing required parameters: email or company');
            }

            const userRef = db.collection('companies')
                           .doc(company)
                           .collection('users')
                           .doc(email);

            // Get user document for latest assessment ID
            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                throw new Error('User not found');
            }

            const userData = userDoc.data();
            const lastAssessmentId = userData.lastAssessmentId;

            if (!lastAssessmentId) {
                throw new Error('No assessment found');
            }

            // Get the actual assessment results
            const assessmentDoc = await userRef
                .collection('assessmentResults')
                .doc(lastAssessmentId)
                .get();

            if (!assessmentDoc.exists) {
                throw new Error('Assessment results not found');
            }

            const assessmentData = assessmentDoc.data();

            return {
                report: {
                    competencyAnalysis: assessmentData.competencyAnalysis,
                    summary: assessmentData.analysisSummary
                },
                recommendations: assessmentData.courseRecommendations?.map(rec => ({
                    course: rec.courseName,
                    reason: rec.justification
                })) || []
            };
        } catch (error) {
            console.error('Error fetching assessment data:', error);
            throw error;
        }
    },

    async showAnalysis(data) {
        try {
            // Check if we have a combined data structure with digital/soft skills
            if (data && ((data.digitalSkills && data.digitalSkills.report && data.digitalSkills.report.competencyAnalysis) ||
                         (data.softSkills && data.softSkills.report && data.softSkills.report.competencyAnalysis) ||
                         (data.report && data.report.competencyAnalysis))) {

                // Show the modal with the provided data
                await showSkillsGapAnalysis(data);
            } else {
                throw new Error('Invalid data structure for skills gap analysis');
            }
        } catch (error) {
            console.error('Error showing skills gap analysis:', error);
            throw error;
        }
    }
};

function loadEnrollmentPage(mainContent, category, params = {}) {
    console.log('Loading enrollment page...', { category, userCompany, params });
    PageLoader.loadEnrollmentPage(mainContent, category, userCompany, params)
        .then(contentType => {
            currentContent = contentType;
            pushToNavigationState(contentType, category, params);
        })
        .catch(error => {
            console.error('Error loading enrollment page:', error);
        });
}

async function loadRecommendations(learner) {
    try {
        const userRef = db.collection('companies').doc(userCompany).collection('users').doc(learner.id);
        const userDoc = await userRef.get();
        const userData = userDoc.data();
        courseRecommendations = Array.isArray(userData.courseRecommendations) ?
            userData.courseRecommendations : [];
        console.log('Loaded recommendations:', courseRecommendations);
    } catch (error) {
        console.error('Error loading course recommendations:', error);
        courseRecommendations = [];
    }
  }



// Remove existing popstate listener and add a new one that supports async
window.removeEventListener('popstate', handlePopState);
window.addEventListener('popstate', (event) => {
    handlePopState(event);
});
window.startDashboardTour = initializeDashboardTour;
