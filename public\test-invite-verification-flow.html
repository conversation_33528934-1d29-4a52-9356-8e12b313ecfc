<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invite Verification Flow Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background-color: #f9fafb;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background-color: #3B82F6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            margin: 0.5rem;
            transition: background-color 0.2s;
        }
        .test-button:hover {
            background-color: #2563EB;
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.375rem;
            background-color: #F3F4F6;
        }
        .console-log {
            background-color: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 0.375rem;
            font-family: monospace;
            font-size: 0.875rem;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Invite Verification Flow Test</h1>
        <p>This page tests the exact scenario from the invite page where email verification is required.</p>
        
        <button class="test-button" onclick="testEmailVerificationFlow()">
            Test Email Verification Required Flow
        </button>
        
        <button class="test-button" onclick="checkModalAvailability()">
            Check Modal Availability
        </button>
        
        <button class="test-button" onclick="clearConsole()">
            Clear Console
        </button>
        
        <div id="result" class="result" style="display: none;">
            <strong>Result:</strong> <span id="result-text"></span>
        </div>

        <div id="console-output" class="console-log">
            <div>Console output will appear here...</div>
        </div>
    </div>

    <!-- Include the modal script -->
    <script src="email-verification-modal.js"></script>
    
    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(type, ...args) {
            const consoleOutput = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ef4444' : type === 'warn' ? '#f59e0b' : '#10b981';
            logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('warn', ...args);
        };

        // Simulate the exact functions from invite.js
        function waitForEmailVerificationModal(timeout = 5000) {
            return new Promise((resolve) => {
                const startTime = Date.now();
                let attempts = 0;
                
                function checkModal() {
                    attempts++;
                    console.log(`Checking for EmailVerificationModal (attempt ${attempts}):`, {
                        windowExists: typeof window !== 'undefined',
                        modalExists: !!window.EmailVerificationModal,
                        showFunction: window.EmailVerificationModal ? typeof window.EmailVerificationModal.show : 'N/A'
                    });
                    
                    if (window.EmailVerificationModal && typeof window.EmailVerificationModal.show === 'function') {
                        console.log('EmailVerificationModal is available!');
                        resolve(true);
                        return;
                    }
                    
                    if (Date.now() - startTime > timeout) {
                        console.error('EmailVerificationModal timeout after', timeout, 'ms');
                        resolve(false);
                        return;
                    }
                    
                    setTimeout(checkModal, 100);
                }
                
                checkModal();
            });
        }

        async function handleEmailVerificationRequired() {
            try {
                // First try immediate check for modal availability
                if (window.EmailVerificationModal && typeof window.EmailVerificationModal.show === 'function') {
                    console.log('EmailVerificationModal available immediately');
                    
                    // Show the email verification modal
                    const userConfirmed = await window.EmailVerificationModal.show({
                        title: 'Email Verification Required',
                        message: 'We\'ve noticed you\'re trying to send an invitation. Please verify your email address first to continue.',
                        confirmText: 'Send Verification Email',
                        cancelText: 'Cancel'
                    });

                    showResult(`User ${userConfirmed ? 'confirmed' : 'cancelled'} the modal`);
                    return;
                }

                // If not immediately available, wait for it
                console.log('EmailVerificationModal not immediately available, waiting...');
                const modalAvailable = await waitForEmailVerificationModal(2000);
                
                if (modalAvailable) {
                    console.log('EmailVerificationModal became available after waiting');
                    
                    // Show the email verification modal
                    const userConfirmed = await window.EmailVerificationModal.show({
                        title: 'Email Verification Required',
                        message: 'We\'ve noticed you\'re trying to send an invitation. Please verify your email address first to continue.',
                        confirmText: 'Send Verification Email',
                        cancelText: 'Cancel'
                    });

                    showResult(`User ${userConfirmed ? 'confirmed' : 'cancelled'} the modal (after waiting)`);
                    return;
                }

                // Fallback to simple confirmation dialog
                console.warn('EmailVerificationModal not available, using fallback confirmation dialog');
                const userConfirmed = confirm(
                    'Email verification is required to send invitations.\n\n' +
                    'Would you like to send a verification email now?'
                );
                
                showResult(`User ${userConfirmed ? 'confirmed' : 'cancelled'} the fallback dialog`);
                
            } catch (error) {
                console.error('Error handling email verification requirement:', error);
                showResult(`Error: ${error.message}`);
            }
        }

        async function testEmailVerificationFlow() {
            console.log('Starting email verification flow test...');
            await handleEmailVerificationRequired();
        }

        function checkModalAvailability() {
            console.log('Checking modal availability...');
            console.log('window.EmailVerificationModal:', window.EmailVerificationModal);
            console.log('typeof window.EmailVerificationModal:', typeof window.EmailVerificationModal);
            if (window.EmailVerificationModal) {
                console.log('window.EmailVerificationModal.show:', window.EmailVerificationModal.show);
                console.log('typeof window.EmailVerificationModal.show:', typeof window.EmailVerificationModal.show);
            }
            
            showResult(`Modal available: ${!!(window.EmailVerificationModal && window.EmailVerificationModal.show)}`);
        }
        
        function showResult(text) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('result-text');
            resultText.textContent = text;
            resultDiv.style.display = 'block';
            
            // Hide after 10 seconds
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 10000);
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = '<div>Console cleared...</div>';
        }

        // Log initial state
        console.log('Test page loaded');
        setTimeout(() => {
            checkModalAvailability();
        }, 100);
    </script>
</body>
</html>
